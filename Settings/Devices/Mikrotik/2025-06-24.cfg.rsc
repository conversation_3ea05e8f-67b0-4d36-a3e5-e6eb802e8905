# 2025-06-24 22:19:52 by RouterOS 7.18.2
# software id = ZNJB-QFUB
#
# model = RB5009UG+S+
# serial number = EC190E86BD6D
/interface bridge
add admin-mac=2C:C8:1B:FF:9D:7E auto-mac=no comment=defconf igmp-snooping=yes \
    name=bridge port-cost-mode=short
/interface ethernet
set [ find default-name=ether1 ] comment="WAN1 - XFinity"
set [ find default-name=ether2 ] comment="Ou Living Room Ubi31" l2mtu=9014 \
    mtu=9000
set [ find default-name=ether3 ] comment="Dn Cabinet Vault" l2mtu=9014 mtu=\
    9000
set [ find default-name=ether4 ] comment="Dn Cabinet PoE" l2mtu=9014 mtu=9000
set [ find default-name=ether5 ] comment="Dn Office Switch" l2mtu=9014 mtu=\
    9000
set [ find default-name=ether6 ] comment="Dn Office Argus" l2mtu=9014 mtu=\
    9000
set [ find default-name=ether7 ] comment="Up Living Room" l2mtu=9014 mtu=9000
set [ find default-name=ether8 ] comment="Up Master Bedroom" l2mtu=9014 mtu=\
    9000
set [ find default-name=sfp-sfpplus1 ] disabled=yes l2mtu=9014 mtu=9000
/interface wireguard
add listen-port=13231 mtu=1420 name=wgchihome
/interface vlan
add arp=proxy-arp comment="Personal Devices" interface=bridge mtu=9000 name=\
    vlan10 vlan-id=10
add comment="IoT Devices" interface=bridge name=vlan20 vlan-id=20
add comment="Media Players" interface=bridge mtu=9000 name=vlan30 vlan-id=30
add comment=Cameras interface=bridge mtu=9000 name=vlan40 vlan-id=40
add comment="Sus Network" interface=bridge name=vlan50 vlan-id=50
add comment="Guest Network" interface=bridge name=vlan60 vlan-id=60
add comment="Core Network" interface=bridge mtu=9000 name=vlan99 vlan-id=99
/interface vrrp
add comment="Pi-hole DNS Floating IP" interface=vlan10 name=vrrp-dns \
    preemption-mode=no priority=50 vrid=53
/interface list
add comment=defconf name=WAN
add comment=defconf name=LAN
/interface wireless security-profiles
set [ find default=yes ] supplicant-identity=MikroTik
/ip pool
add name=dhcp ranges=*************-*************
add name=dhcp-vlan99 ranges=***********-***********
add name=dhcp-vlan10 ranges=***********-***********
add name=dhcp-vlan20 ranges=**********0-**********4
add name=dhcp-vlan30 ranges=***********-***********
add name=dhcp-vlan40 ranges=**********0-**********4
add name=dhcp-vlan50 ranges=***********-***********
add name=dhcp-vlan60 ranges=**********-***********
/ip dhcp-server
add add-arp=yes address-pool=dhcp bootp-support=none interface=bridge \
    lease-time=10m name=defconf
add add-arp=yes address-pool=dhcp-vlan10 bootp-support=none interface=vlan10 \
    lease-time=10m name=dhcp-vlan10
add add-arp=yes address-pool=dhcp-vlan20 bootp-support=none interface=vlan20 \
    lease-time=10m name=dhcp-vlan20
add add-arp=yes address-pool=dhcp-vlan30 bootp-support=none interface=vlan30 \
    lease-time=10m name=dhcp-vlan30
add add-arp=yes address-pool=dhcp-vlan40 bootp-support=none interface=vlan40 \
    lease-time=10m name=dhcp-vlan40
add add-arp=yes address-pool=dhcp-vlan50 bootp-support=none interface=vlan50 \
    lease-time=10m name=dhcp-vlan50
add add-arp=yes address-pool=dhcp-vlan99 bootp-support=none interface=vlan99 \
    lease-time=10m name=dhcp-vlan99
add add-arp=yes address-pool=dhcp-vlan60 bootp-support=none interface=vlan60 \
    lease-time=10m name=dhcp-vlan60
/user group
add name=homeassistant policy="read,test,api,!local,!telnet,!ssh,!ftp,!reboot,\
    !write,!policy,!winbox,!password,!web,!sniff,!sensitive,!romon,!rest-api"
/ip smb
set domain=OurHome
/interface bridge port
add bridge=bridge comment=defconf interface=ether3 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether4 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether5 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether6 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether7 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether8 internal-path-cost=10 \
    path-cost=10
add bridge=bridge comment=defconf interface=ether2 internal-path-cost=10 \
    path-cost=10
/ip neighbor discovery-settings
set discover-interface-list=LAN
/ip settings
set arp-timeout=1h max-neighbor-entries=8192
/ipv6 settings
set accept-redirects=no accept-router-advertisements=no disable-ipv6=yes \
    forward=no max-neighbor-entries=8192 soft-max-neighbor-entries=8191
/interface detect-internet
set lan-interface-list=LAN wan-interface-list=WAN
/interface list member
add comment="Home LAN" interface=bridge list=LAN
add comment=XFinity interface=ether1 list=WAN
add comment="Personal Devices VLAN 10" interface=vlan10 list=LAN
add comment="IoT Devices VLAN 20" interface=vlan20 list=LAN
add comment="Media Players VLAN 30" interface=vlan30 list=LAN
add comment="Cameras VLAN 40" interface=vlan40 list=LAN
add comment="Sus Network VLAN 50" interface=vlan50 list=LAN
add comment="Core Network VLAN 99" interface=vlan99 list=LAN
add comment="Guest Network VLAN 60" interface=vlan60 list=LAN
add comment="VPN road-warrior clients" interface=wgchihome list=LAN
/interface wireguard peers
add allowed-address=*********62/32 comment="Chi iPhone 13" interface=\
    wgchihome name=peer2 persistent-keepalive=15s public-key=\
    "zh/8YzyClrIfttrlP2RibR7r9Jco9qTrfgLmCRc+l20="
add allowed-address=*********63/32 comment="Uli iPhone 13" interface=\
    wgchihome name=peer3 persistent-keepalive=15s public-key=\
    "Z8ay3TF55iEReQcu+eO6aLIFatJo/pi/MkLaIGFKLjc="
add allowed-address=*********64/32 comment="Nav iPhone 13" interface=\
    wgchihome name=peer4 persistent-keepalive=15s public-key=\
    "yWYpDY1aLMM4cm5z4HhsyzhsW0PEj5MfW/sBtacNUzM="
add allowed-address=*********61/32 comment="Chi MBA" interface=wgchihome \
    name=peer1 persistent-keepalive=15s public-key=\
    "xAoJvfXi7H6jmndgLln+UV+U8NZFmQYCNclgG7n6Rm8="
/ip address
add address=***********/24 comment=defconf interface=bridge network=\
    ***********
add address=*********/24 comment=Core interface=vlan99 network=*********
add address=*********/24 comment=Personal interface=vlan10 network=*********
add address=*********/24 comment=IoT interface=vlan20 network=*********
add address=*********/24 comment=Media interface=vlan30 network=*********
add address=*********/24 comment=Camera interface=vlan40 network=*********
add address=*********/24 comment=Sus interface=vlan50 network=*********
add address=*********/24 comment=Guest interface=vlan60 network=*********
add address=**********/24 comment="Media > HomeAssistant proxy" interface=\
    vlan30 network=*********
add address=*********74/28 comment="Personal > Road-warrior VPN" interface=\
    wgchihome network=*********60
add address=********** comment="Pi-hole DNS Floating IP" interface=vrrp-dns \
    network=**********
/ip dhcp-client
add comment=defconf interface=ether1 use-peer-dns=no
/ip dhcp-server lease
add address=*********1 comment="Ubi01 Dn Cabinet" mac-address=\
    24:5A:4C:8C:30:C5 server=dhcp-vlan99
add address=*********** comment="CableCreation device" mac-address=\
    A0:CE:C8:EB:E4:C6 server=dhcp-vlan10
add address=*********** comment="Artisan Craft Tablet" mac-address=\
    9C:30:5B:C1:33:11 server=dhcp-vlan10
add address=*********** comment="CMNas Synology" mac-address=\
    00:11:32:24:E8:38 server=dhcp-vlan10
add address=*********3 comment="Ubi03 Up Living Room" mac-address=\
    24:5A:4C:7A:80:CE server=dhcp-vlan99
add address=*********2 comment="Ubi02 Dn Office" mac-address=\
    78:45:58:67:94:D8 server=dhcp-vlan99
add address=*********4 comment="Ubi04 Up Art Room" mac-address=\
    78:45:58:67:8E:70 server=dhcp-vlan99
add address=*********5 comment="Ubi05 Up Master Bedroom" mac-address=\
    78:45:58:67:8B:A4 server=dhcp-vlan99
add address=*********6 comment="Ubi06 Detached Garage" mac-address=\
    78:45:58:67:33:B8 server=dhcp-vlan99
add address=*********2 comment="ATV Dn Gym" mac-address=90:DD:5D:8D:94:96 \
    server=dhcp-vlan30
add address=*********** comment="Chirag iPad 2" mac-address=24:A2:E1:29:1F:1F \
    server=dhcp-vlan10
add address=*********6 comment="Xfinity FlexBox" mac-address=\
    F0:46:3B:3E:2A:90 server=dhcp-vlan30
add address=********** comment="ATV Travel" mac-address=F0:B3:EC:0B:52:ED \
    server=dhcp-vlan30
add address=*********4 comment="Frame 75 Up Liv TV" mac-address=\
    80:47:86:E5:BC:B9 server=dhcp-vlan30
add address=*********5 comment="XBox Up Living Room" mac-address=\
    4C:3B:DF:59:98:E6 server=dhcp-vlan30
add address=*********7 comment="ATV Up Art Room" mac-address=\
    1C:B3:C9:12:FA:19 server=dhcp-vlan30
add address=*********9 comment="ATV Up Master Bedroom" mac-address=\
    EC:A9:07:1C:73:A2 server=dhcp-vlan30
add address=*********1 comment="ATV Dn EnSuite" mac-address=08:66:98:ED:DB:BC \
    server=dhcp-vlan30
add address=********** comment="Sonos Dn Living Room" mac-address=\
    48:A6:B8:27:C4:9C server=dhcp-vlan30
add address=********** comment="ATV Dn Living Room" mac-address=\
    34:FD:6A:0F:3E:CA server=dhcp-vlan30
add address=*********3 comment="Cam13 Up Entrance" mac-address=\
    78:66:9D:14:02:F6 server=dhcp-vlan40
add address=*********4 comment="Cam14 Back Ponds" mac-address=\
    78:66:9D:14:53:18 server=dhcp-vlan40
add address=*********2 comment="Cam12 Front Garage" mac-address=\
    60:FB:00:87:A1:20 server=dhcp-vlan40
add address=*********** comment="Argus Blue Iris Server" mac-address=\
    EC:8E:B5:77:69:08 server=dhcp-vlan10
add address=*********7 comment="Ubi07 Front Small Pond" mac-address=\
    60:22:32:3D:1F:48 server=dhcp-vlan99
add address=*********8 comment="Ubi08 Front Large Pond" mac-address=\
    60:22:32:3B:FC:7C server=dhcp-vlan99
add address=*********9 comment="Ubi09 Driveway Middle" mac-address=\
    60:22:32:3D:20:2C server=dhcp-vlan99
add address=********** comment="Ubi10 Spare" mac-address=60:22:32:3D:12:E0 \
    server=dhcp-vlan99
add address=*********3 comment="ATV Up Living Room" mac-address=\
    A8:51:AB:9E:45:F1 server=dhcp-vlan30
add address=********** comment="Frame 55 Up MBr TV" mac-address=\
    A0:D0:5B:62:E5:6A server=dhcp-vlan30
add address=*********5 comment="Cam15 Driveway Entrance" mac-address=\
    EC:71:DB:E0:D5:BE server=dhcp-vlan40
add address=*********** comment="Naveen Chromebook" mac-address=\
    B0:A4:60:AF:6B:A8 server=dhcp-vlan10
add address=*********** comment="Arceus Gaming PC" mac-address=\
    10:6F:D9:BC:AC:71 server=dhcp-vlan10
add address=*********** comment="Arrival Mailbox Pi" mac-address=\
    B8:27:EB:24:88:96 server=dhcp-vlan10
add address=*********6 comment="Cam16 Small Pond Aerator" mac-address=\
    68:76:27:2C:15:A4 server=dhcp-vlan40
add address=*********8 comment="Cam18 Large Pond Aerator" mac-address=\
    68:76:27:2C:8F:4F server=dhcp-vlan40
add address=********** comment="Ubi31 Nano5 Up Living Room" mac-address=\
    E4:38:83:B2:42:62 server=dhcp-vlan99
add address=*********** comment="Naveen Fire 10 Tablet" mac-address=\
    58:E4:88:6A:9A:10 server=dhcp-vlan10
add address=********** comment="Ubi32 Nano5 Ou Driveway Entrance" \
    mac-address=E4:38:83:C2:48:A6 server=dhcp-vlan99
add address=********** comment="Cam20 Driveway Middle" mac-address=\
    C4:3C:B0:9A:9B:5E server=dhcp-vlan40
add address=********** comment="Cam21 Master Bath Cat" mac-address=\
    9C:8E:CD:19:B7:78 server=dhcp-vlan40
add address=********** comment="Cam22 Master Bath Cat" mac-address=\
    9C:8E:CD:19:F1:AB server=dhcp-vlan40
add address=*********** comment="Chi Mac Mini Wired" disabled=yes \
    mac-address=00:C5:85:00:48:AA server=dhcp-vlan10
add address=********** comment="Ubi33 Nano5 Ou Driveway Middle" mac-address=\
    F4:E2:C6:8C:E7:9D server=dhcp-vlan99
add address=*********** comment="Naveen Kindle Reader" mac-address=\
    14:0A:C5:5F:54:4C server=dhcp-vlan10
add address=*********** comment="Chirag reMarkable2" mac-address=\
    20:50:E7:2E:D1:20 server=dhcp-vlan10
add address=*********** comment="Leela iPhone Xs" mac-address=\
    AE:E1:EB:53:86:A1 server=dhcp-vlan10
add address=*********** comment="Naveen iPhone 16 Pro" mac-address=\
    A2:B5:F4:29:8D:C5 server=dhcp-vlan10
add address=*********** comment="Juliet iPhone 16 Pro" mac-address=\
    56:A9:9F:62:92:8C server=dhcp-vlan10
add address=*********1 comment="Cam11 Dn Entrance" mac-address=\
    9C:95:61:A7:88:72 server=dhcp-vlan40
add address=*********** comment="Chirag MacMini M2 VLAN10" mac-address=\
    00:C5:85:00:48:AA server=dhcp-vlan10
add address=*********** comment="Chirag MacBook Air M2" mac-address=\
    A2:70:59:D8:B0:82 server=dhcp-vlan10
add address=*********** comment="Juliet Apple Watch" mac-address=\
    E2:B5:93:8C:18:9A server=dhcp-vlan10
add address=*********** comment="Chirag MacMini M2 Wifi" mac-address=\
    C2:B5:1F:D8:39:B0 server=dhcp-vlan10
add address=*********0 comment="SimpliSafe Base Station" mac-address=\
    F8:51:28:BE:BF:60 server=dhcp-vlan20
add address=*********01 comment=Meross101 mac-address=48:E1:E9:69:06:88 \
    server=dhcp-vlan20
add address=*********02 comment=Meross102 mac-address=48:E1:E9:64:C8:58 \
    server=dhcp-vlan20
add address=*********03 comment=Meross103 mac-address=48:E1:E9:77:0F:87 \
    server=dhcp-vlan20
add address=*********04 comment=Meross104 mac-address=48:E1:E9:69:04:7D \
    server=dhcp-vlan20
add address=*********05 comment=Meross105 mac-address=48:E1:E9:77:0A:2F \
    server=dhcp-vlan20
add address=*********06 comment=Meross106 mac-address=48:E1:E9:77:1D:F1 \
    server=dhcp-vlan20
add address=*********07 comment=Meross107 mac-address=48:E1:E9:77:1D:11 \
    server=dhcp-vlan20
add address=*********08 comment=Meross108 mac-address=48:E1:E9:73:B2:6F \
    server=dhcp-vlan20
add address=*********09 comment=Meross109 mac-address=48:E1:E9:77:CA:8C \
    server=dhcp-vlan20
add address=*********1 comment="Shelly NatGas Up Kitchen" mac-address=\
    10:52:1C:F2:DA:AD server=dhcp-vlan20
add address=*********10 comment=Meross110 mac-address=48:E1:E9:77:C6:A4 \
    server=dhcp-vlan20
add address=*********11 comment=Meross111 mac-address=48:E1:E9:77:E7:12 \
    server=dhcp-vlan20
add address=*********12 comment=Meross112 mac-address=48:E1:E9:77:C9:01 \
    server=dhcp-vlan20
add address=*********13 comment=Meross113 mac-address=48:E1:E9:37:7E:6F \
    server=dhcp-vlan20
add address=*********14 comment=Meross114 mac-address=48:E1:E9:37:2E:1B \
    server=dhcp-vlan20
add address=*********15 comment=Meross115 mac-address=48:E1:E9:7A:0B:B8 \
    server=dhcp-vlan20
add address=*********16 comment=Meross116 mac-address=48:E1:E9:4B:BE:B6 \
    server=dhcp-vlan20
add address=*********17 comment=Meross117 mac-address=48:E1:E9:77:15:0B \
    server=dhcp-vlan20
add address=*********18 comment=Meross118 mac-address=48:E1:E9:A7:C7:F1 \
    server=dhcp-vlan20
add address=*********19 comment=Meross119 mac-address=48:E1:E9:A7:D2:70 \
    server=dhcp-vlan20
add address=*********2 comment="Shelly NatGas Dn Laundry" mac-address=\
    50:02:91:78:B5:20 server=dhcp-vlan20
add address=*********20 comment=Meross120 mac-address=48:E1:E9:77:14:84 \
    server=dhcp-vlan20
add address=*********21 comment=Meross121 mac-address=48:E1:E9:4B:BF:1C \
    server=dhcp-vlan20
add address=*********22 comment=Meross122 mac-address=48:E1:E9:E0:2A:81 \
    server=dhcp-vlan20
add address=*********23 comment=Meross123 mac-address=48:E1:E9:7A:0B:E2 \
    server=dhcp-vlan20
add address=*********24 comment=Meross124 mac-address=48:E1:E9:64:C9:A6 \
    server=dhcp-vlan20
add address=*********25 comment=Meross125 mac-address=48:E1:E9:77:0C:44 \
    server=dhcp-vlan20
add address=*********26 comment=Meross126 mac-address=48:E1:E9:7A:0A:48 \
    server=dhcp-vlan20
add address=*********27 comment=Meross127 mac-address=48:E1:E9:4B:C5:23 \
    server=dhcp-vlan20
add address=*********28 comment=Meross128 mac-address=48:E1:E9:64:C7:C1 \
    server=dhcp-vlan20
add address=*********29 comment=Meross129 mac-address=48:E1:E9:77:17:90 \
    server=dhcp-vlan20
add address=*********3 comment="MyQ Main Garage" mac-address=\
    24:18:C6:33:8D:09 server=dhcp-vlan20
add address=*********32 comment=Meross132 mac-address=48:E1:E9:77:1F:4D \
    server=dhcp-vlan20
add address=*********33 comment=Meross133 mac-address=48:E1:E9:7A:20:5C \
    server=dhcp-vlan20
add address=*********34 comment=Meross134 mac-address=48:E1:E9:64:CA:CE \
    server=dhcp-vlan20
add address=*********35 comment=Meross135 mac-address=48:E1:E9:64:CA:D2 \
    server=dhcp-vlan20
add address=*********36 comment=Meross136 mac-address=48:E1:E9:64:C9:DD \
    server=dhcp-vlan20
add address=*********37 comment=Meross137 mac-address=48:E1:E9:64:C6:55 \
    server=dhcp-vlan20
add address=*********38 comment=Meross138 mac-address=48:E1:E9:64:C4:BB \
    server=dhcp-vlan20
add address=*********39 comment=Meross139 mac-address=48:E1:E9:A7:D2:94 \
    server=dhcp-vlan20
add address=*********4 comment="Whirlpool Dryer Dn Laundry" mac-address=\
    C4:DD:57:C1:9B:1C server=dhcp-vlan20
add address=*********40 comment=Meross140 mac-address=48:E1:E9:E0:2C:B7 \
    server=dhcp-vlan20
add address=*********41 comment=Meross141 mac-address=48:E1:E9:4B:BF:4D \
    server=dhcp-vlan20
add address=*********42 comment=Meross142 mac-address=48:E1:E9:4B:D0:3A \
    server=dhcp-vlan20
add address=*********43 comment=Meross143 mac-address=48:E1:E9:4B:BF:2A \
    server=dhcp-vlan20
add address=*********44 comment=Meross144 mac-address=48:E1:E9:77:1F:3C \
    server=dhcp-vlan20
add address=*********45 comment=Meross145 mac-address=48:E1:E9:A7:CA:82 \
    server=dhcp-vlan20
add address=*********46 comment=Meross146 mac-address=48:E1:E9:A7:D8:A9 \
    server=dhcp-vlan20
add address=*********47 comment=Meross147 mac-address=48:E1:E9:77:16:DE \
    server=dhcp-vlan20
add address=*********48 comment=Meross148 mac-address=48:E1:E9:7A:0B:B7 \
    server=dhcp-vlan20
add address=*********49 comment=Meross149 mac-address=48:E1:E9:7A:0B:8A \
    server=dhcp-vlan20
add address=*********5 comment="Whirlpool Washer Dn Laundry" mac-address=\
    98:CD:AC:AC:DE:00 server=dhcp-vlan20
add address=*********50 comment=Meross150 mac-address=48:E1:E9:7A:09:93 \
    server=dhcp-vlan20
add address=*********51 comment=Meross151 mac-address=48:E1:E9:7A:0E:6E \
    server=dhcp-vlan20
add address=*********52 comment=Meross152 mac-address=48:E1:E9:77:1E:4D \
    server=dhcp-vlan20
add address=*********53 comment=Meross153 mac-address=48:E1:E9:77:0F:5A \
    server=dhcp-vlan20
add address=*********54 comment=Meross154 mac-address=48:E1:E9:77:19:50 \
    server=dhcp-vlan20
add address=*********55 comment=Meross155 mac-address=48:E1:E9:77:10:E4 \
    server=dhcp-vlan20
add address=*********56 comment=Meross156 mac-address=48:E1:E9:77:0A:17 \
    server=dhcp-vlan20
add address=*********57 comment=Meross157 mac-address=48:E1:E9:7A:24:A0 \
    server=dhcp-vlan20
add address=*********58 comment=Meross158 mac-address=48:E1:E9:7A:10:51 \
    server=dhcp-vlan20
add address=*********59 comment=Meross159 mac-address=48:E1:E9:7A:10:3C \
    server=dhcp-vlan20
add address=*********6 comment="Ecobee Up Dining Room" mac-address=\
    44:61:32:AA:79:58 server=dhcp-vlan20
add address=*********60 comment=Meross160 mac-address=48:E1:E9:4B:D1:9B \
    server=dhcp-vlan20
add address=*********61 comment=Meross161 mac-address=48:E1:E9:4B:C8:7F \
    server=dhcp-vlan20
add address=*********62 comment=Meross162 mac-address=48:E1:E9:64:C7:AC \
    server=dhcp-vlan20
add address=*********63 comment=Meross163 mac-address=48:E1:E9:4B:BB:EF \
    server=dhcp-vlan20
add address=*********64 comment=Meross164 mac-address=48:E1:E9:4B:B7:90 \
    server=dhcp-vlan20
add address=*********65 comment=Meross165 mac-address=48:E1:E9:70:84:11 \
    server=dhcp-vlan20
add address=*********66 comment=Meross166 mac-address=48:E1:E9:7A:1F:1C \
    server=dhcp-vlan20
add address=*********67 comment=Meross167 mac-address=48:E1:E9:7A:13:8B \
    server=dhcp-vlan20
add address=*********68 comment=Meross168 mac-address=48:E1:E9:7A:10:B0 \
    server=dhcp-vlan20
add address=*********69 comment=Meross169 mac-address=48:E1:E9:7A:11:FC \
    server=dhcp-vlan20
add address=*********7 comment="Ecobee Up Art Room" mac-address=\
    44:61:32:8B:F0:C4 server=dhcp-vlan20
add address=*********70 comment=Meross170 mac-address=48:E1:E9:7A:1D:7C \
    server=dhcp-vlan20
add address=*********71 comment=Meross171 mac-address=48:E1:E9:77:07:95 \
    server=dhcp-vlan20
add address=*********72 comment=Meross172 mac-address=48:E1:E9:7A:10:2A \
    server=dhcp-vlan20
add address=*********73 comment=Meross173 mac-address=48:E1:E9:4B:C5:2B \
    server=dhcp-vlan20
add address=*********** comment=Meross174 mac-address=48:E1:E9:4B:C4:D4 \
    server=dhcp-vlan20
add address=*********** comment=Meross175 mac-address=48:E1:E9:4B:C4:C9 \
    server=dhcp-vlan20
add address=*********** comment=Meross176 mac-address=48:E1:E9:77:0A:16 \
    server=dhcp-vlan20
add address=*********** comment=Meross177 mac-address=48:E1:E9:7A:10:37 \
    server=dhcp-vlan20
add address=*********** comment=Meross178 mac-address=48:E1:E9:A7:C6:5F \
    server=dhcp-vlan20
add address=*********** comment=Meross179 mac-address=48:E1:E9:A7:D8:84 \
    server=dhcp-vlan20
add address=********** comment="Ecobee Up MBR Lobby" mac-address=\
    44:61:32:AA:0D:BB server=dhcp-vlan20
add address=**********1 comment=Meross181 mac-address=48:E1:E9:78:6E:4A \
    server=dhcp-vlan20
add address=**********2 comment=Meross182 mac-address=48:E1:E9:78:6C:21 \
    server=dhcp-vlan20
add address=**********3 comment=Meross183 mac-address=48:E1:E9:78:6A:5F \
    server=dhcp-vlan20
add address=********** comment="MyQ Detached Garage 1" mac-address=\
    64:52:99:EB:54:20 server=dhcp-vlan20
add address=********** comment="MyQ Detached Garage 2" mac-address=\
    64:52:99:EB:4E:41 server=dhcp-vlan20
add address=********** comment="MyQ Detached Garage 3" mac-address=\
    64:52:99:EB:4D:6E server=dhcp-vlan20
add address=********** comment="iFire Fireplace Up Dining" mac-address=\
    4C:55:CC:26:51:92 server=dhcp-vlan20
add address=********** comment="Up Master Bathroom Roller" mac-address=\
    38:1F:8D:55:C2:0C server=dhcp-vlan20
add address=********** comment="Roller Hub Up Master Bedroom" mac-address=\
    E8:68:E7:07:F2:5C server=dhcp-vlan20
add address=********** comment="Up Master Bedroom Halo LED" mac-address=\
    10:5A:17:72:93:BC server=dhcp-vlan20
add address=********** comment="BroadLink RM4 Master Bedroom" mac-address=\
    A0:43:B0:70:22:CB server=dhcp-vlan20
add address=********** comment="Dn Guest Room Door Roller" mac-address=\
    38:1F:8D:55:C1:09 server=dhcp-vlan20
add address=********** comment="Dn EnSuite Window Small Roller" mac-address=\
    CC:8C:BF:3B:F9:C0 server=dhcp-vlan20
add address=********** comment="Dn EnSuite Window Large Roller" mac-address=\
    CC:8C:BF:3B:F0:BF server=dhcp-vlan20
add address=********** comment="BroadLink RM4 Dn Living Room" mac-address=\
    A0:43:B0:72:C9:3A server=dhcp-vlan20
add address=********** comment="Samsung Range Dn Kitchen" mac-address=\
    40:CA:63:4A:54:05 server=dhcp-vlan20
add address=********** comment="Litter Robot 4 Up Master Bath" mac-address=\
    40:22:D8:E4:A0:70 server=dhcp-vlan20
add address=********** comment="Feeder Merlin Up MBR" mac-address=\
    FC:67:1F:3B:58:1B server=dhcp-vlan20
add address=********** comment="Owlet Base Station" mac-address=\
    24:CD:8D:ED:C6:82 server=dhcp-vlan20
add address=********** comment="Emporia Level 2 Charger" mac-address=\
    34:5F:45:BA:10:28 server=dhcp-vlan20
add address=********* comment="SimpliSafe Base Station" mac-address=\
    F8:51:28:01:99:B1 server=dhcp-vlan20
add address=********* comment="SimpliSafe Base Station" mac-address=\
    F8:51:28:02:B3:75 server=dhcp-vlan20
add address=*********** comment="Vault QNAP" mac-address=26:5E:B4:6A:56:7F \
    server=dhcp-vlan10
add address=*********** comment="Arachne Docker Host" mac-address=\
    18:60:24:DB:12:0B server=dhcp-vlan10
add address=*********** comment="BWHome Printer WiFi" mac-address=\
    00:1F:E1:06:23:F2 server=dhcp-vlan10
add address=********** comment="SW01 Dn Cabinet" mac-address=\
    DC:62:79:3A:FA:87 server=dhcp-vlan99
add address=********* comment="Ooma VOIP" mac-address=00:18:61:57:0A:D9 \
    server=dhcp-vlan99
add address=********** comment="SW02 Up Living Room" mac-address=\
    DC:62:79:3A:FA:38 server=dhcp-vlan99
add address=********** comment="SW03 Up Master Bedroom" mac-address=\
    EC:75:0C:FF:FA:9A server=dhcp-vlan99
add address=********** comment="SW04 Ou Driveway Entrance" mac-address=\
    EC:75:0C:FF:FD:D7 server=dhcp-vlan99
add address=********** comment="RatGDO1 Dn Main Garage" mac-address=\
    F0:24:F9:E1:69:2C server=dhcp-vlan20
add address=********** comment="RatGDO2 Ou Detached Garage 1" mac-address=\
    F0:24:F9:E1:5E:44 server=dhcp-vlan20
add address=********** comment="RatGDO3 Ou Detached Garage 2" mac-address=\
    D4:8C:49:A1:50:38 server=dhcp-vlan20
add address=********** comment="RatGDO4 Ou Detached Garage 3" mac-address=\
    90:15:06:9A:08:20 server=dhcp-vlan20
add address=********** comment="Cam23 Up Living Room Crib" mac-address=\
    3C:C5:DD:56:67:5A server=dhcp-vlan40
add address=********** comment="Cam24 Up Living Room Swing" mac-address=\
    3C:C5:DD:56:79:D2 server=dhcp-vlan40
add address=********** comment="Dn Gym Back PIR" mac-address=\
    B8:06:0D:37:B6:C2 server=dhcp-vlan20
add address=**********4 comment=Meross184 mac-address=48:E1:E9:EA:44:AD \
    server=dhcp-vlan20
add address=**********5 comment=Meross185 mac-address=48:E1:E9:EA:3E:D1 \
    server=dhcp-vlan20
add address=*********** comment="Chirag Apple Watch" mac-address=\
    52:D5:33:EC:50:E8 server=dhcp-vlan10
add address=*********** comment="Chirag iPhone 16 Pro Max" mac-address=\
    86:27:DF:4A:A5:32 server=dhcp-vlan10
add address=********** comment="Cam17  Driveway Bend" mac-address=\
    14:EA:63:3E:50:AD server=dhcp-vlan40
add address=********** comment="Cam19 Guest Patio" mac-address=\
    0C:79:55:4B:BE:9C server=dhcp-vlan40
add address=********** comment="Cam25 Kitchen Patio" mac-address=\
    14:EA:63:3E:51:7D server=dhcp-vlan40
add address=********** comment="Cam26 Master Patio" mac-address=\
    0C:79:55:4B:CC:40 server=dhcp-vlan40
add address=********** comment="Emporia041 Dn Cabinet Devices" mac-address=\
    D8:BC:38:BC:ED:2F server=dhcp-vlan20
add address=********** comment="Emporia042 Dn Cabinet Audio" mac-address=\
    D8:BC:38:BC:9B:6D server=dhcp-vlan20
add address=********** comment="Emporia043 Up Living Room Media" mac-address=\
    D8:BC:38:BC:B7:1A server=dhcp-vlan20
add address=********** comment="Emporia044 Up Master Bedroom Media" \
    mac-address=D8:BC:38:BC:3D:19 server=dhcp-vlan20
/ip dhcp-server network
add address=*********/24 comment=VLAN99 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN10 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN20 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN30 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN40 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN50 dns-server=********** gateway=\
    ********* netmask=24 ntp-server=*********
add address=*********/24 comment=VLAN60 dns-server=*******,******* gateway=\
    ********* netmask=24 ntp-server=*********
add address=***********/24 comment=defconf dns-server=********** gateway=\
    *********** netmask=24 ntp-server=***********
/ip dns
set allow-remote-requests=yes mdns-repeat-ifaces=vlan10,vlan30 \
    query-server-timeout=1s servers=*******,*******
/ip firewall address-list
add address=*********** comment="CMNas Synology" list=block-wan
add address=********** comment="Roller Hub Up Master Bedroom" list=block-wan
add address=********** comment="BroadLink RM4 Up MBR" list=block-wan
add address=********** comment="BroadLink RM4 Dn Living Room" list=block-wan
add address=********** comment="Samsung Range Dn Kitchen" list=block-wan
add address=*********/24 comment="VLAN40 - Cameras" list=block-wan
add address=*********/24 comment="VLAN50 - Sus Network" list=block-wan
add address=*********4 comment="Samsung Frame 75 Up Living Room TV" list=\
    samsung-tv
add address=********** comment="Samsung Frame 55 Up MBR" list=samsung-tv
add address=***********/24 comment="Quarantined Subnet" list=quarantined-lan
add address=*********** comment="Firewall - HA" list=firewall-ha
add address=*********** comment="Firewall - Argus" list=firewall-argus
add address=*********61-*********69 comment="Wireguard Peers" list=wg-peers
/ip firewall filter
add action=drop chain=forward comment="defconf: drop invalid" \
    connection-state=invalid
add action=drop chain=forward comment="CM: Block WAN for specific IPs" \
    out-interface-list=WAN src-address-list=block-wan
add action=drop chain=forward comment=\
    "CM: Block LAN/WAN to Quarantine subnet" dst-address-list=quarantined-lan
add action=drop chain=forward comment=\
    "CM: Block LAN/WAN for Quarantine subnet" src-address-list=\
    quarantined-lan
add action=fasttrack-connection chain=forward comment="defconf: fasttrack" \
    connection-state=established,related hw-offload=yes
add action=accept chain=forward comment=\
    "defconf: accept established,related, untracked" connection-state=\
    established,related,untracked
add action=accept chain=forward comment="CM: Allow WireGuard VPN" \
    in-interface=wgchihome
add action=accept chain=forward comment=\
    "CM: Allow LAN (except Sus) to Home-Assistant" dst-address-list=\
    firewall-ha in-interface=!vlan50 in-interface-list=LAN
add action=accept chain=forward comment="CM: Allow VLAN10 to any other LAN" \
    in-interface=vlan10 out-interface-list=LAN
add action=accept chain=forward comment="CM: Allow VLAN30 to VLAN10 " \
    in-interface=vlan30 out-interface=vlan10
add action=accept chain=forward comment="CM: Allow VLAN40 (Cams) to Argus" \
    dst-address-list=firewall-argus in-interface=vlan40
add action=accept chain=forward comment="CM: Allow ICMP responses to VLAN10" \
    in-interface-list=LAN out-interface=vlan10 protocol=icmp
add action=accept chain=forward comment="CM: Allow Inter-VLAN Traffic" \
    in-interface-list=LAN log=yes log-prefix=HIT_LAN2LAN out-interface-list=\
    LAN
add action=drop chain=forward comment=\
    "defconf: drop all from WAN not DSTNATed" connection-nat-state=!dstnat \
    connection-state=new in-interface-list=WAN
add action=accept chain=forward comment="CM: Allow LAN-to-WAN new" \
    connection-state=new in-interface-list=LAN out-interface-list=WAN
add action=drop chain=forward comment="CM: Block WAN for anything remaining" \
    log=yes log-prefix=HIT_WAN_BLOCK
add action=accept chain=input comment=\
    "defconf: accept established,related,untracked" connection-state=\
    established,related,untracked
add action=drop chain=input comment="defconf: drop invalid" connection-state=\
    invalid
add action=accept chain=input comment="defconf: accept ICMP" \
    in-interface-list=LAN protocol=icmp
add action=accept chain=input comment="CM: Allow NTP" dst-port=123 \
    in-interface-list=LAN protocol=udp
add action=accept chain=input comment="CM: Allow Wireguard Chi Home" \
    dst-port=13231 protocol=udp
add action=accept chain=input comment="CM: Allow DHCP for Quarantine subnet" \
    dst-port=67-68 in-interface=bridge protocol=udp
add action=accept chain=input comment="CM: Pi-hole DNS TCP on floating IP" \
    dst-port=53 in-interface=vrrp-dns protocol=tcp
add action=accept chain=input comment="CM: Pi-hole DNS UDP on floating IP" \
    dst-port=53 in-interface=vrrp-dns protocol=udp
add action=accept chain=input comment=\
    "CM: Allow LAN TCP for Quarantine subnet" dst-port=53,8291 protocol=tcp \
    src-address-list=quarantined-lan
add action=accept chain=input comment=\
    "CM: Allow LAN UDP for Quarantine subnet" dst-port=53,67-68,123 protocol=\
    udp src-address-list=quarantined-lan
add action=drop chain=input comment="defconf: drop all not coming from LAN" \
    in-interface-list=!LAN
/ip firewall nat
add action=masquerade chain=srcnat comment="defconf: masquerade" \
    ipsec-policy=out,none out-interface-list=WAN
add action=src-nat chain=srcnat comment="SNAT HA (***********) to Media VLAN I\
    P (**********) for Samsung TV WebSocket ports" dst-address-list=\
    samsung-tv dst-port=8001,8002,9197 protocol=tcp src-address=*********** \
    to-addresses=**********
add action=dst-nat chain=dstnat comment=\
    "CM: Make 443/udp reach wgchihome (13231)" dst-port=443 \
    in-interface-list=WAN protocol=udp to-ports=13231
/ip ipsec profile
set [ find default=yes ] dpd-interval=2m dpd-maximum-failures=5
/ip smb shares
set [ find default=yes ] directory=/pub
/ipv6 firewall address-list
add address=::/128 comment="defconf: unspecified address" list=bad_ipv6
add address=::1/128 comment="defconf: lo" list=bad_ipv6
add address=fec0::/10 comment="defconf: site-local" list=bad_ipv6
add address=::ffff:0.0.0.0/96 comment="defconf: ipv4-mapped" list=bad_ipv6
add address=::/96 comment="defconf: ipv4 compat" list=bad_ipv6
add address=100::/64 comment="defconf: discard only " list=bad_ipv6
add address=2001:db8::/32 comment="defconf: documentation" list=bad_ipv6
add address=2001:10::/28 comment="defconf: ORCHID" list=bad_ipv6
add address=3ffe::/16 comment="defconf: 6bone" list=bad_ipv6
/routing igmp-proxy interface
add alternative-subnets=0.0.0.0/0 interface=vlan30
add alternative-subnets=0.0.0.0/0 interface=vlan10 upstream=yes
/system clock
set time-zone-name=America/Chicago
/system note
set show-at-login=no
/system ntp client
set enabled=yes
/system ntp server
set enabled=yes multicast=yes
/system ntp client servers
add address=time.nist.gov
add address=time.google.com
add address=time.windows.com
/system routerboard settings
set cpu-frequency=1400MHz
/system scheduler
add comment="Update Dynu DDNS" interval=5m name=ddns_sheduller on-event=\
    "/system script run dynu" policy=\
    ftp,reboot,read,write,policy,test,password,sniff,sensitive,romon \
    start-time=startup
/system script
add dont-require-permissions=no name=dynu owner=admin policy=\
    ftp,reboot,read,write,policy,test,password,sniff,sensitive,romon source="#\
    \_get the current IP address from the internet\r\
    \n/tool fetch mode=http address=\"checkip.dynu.com\" src-path=\"/\" dst-pa\
    th=\"/dynu.checkip.html\"\r\
    \n:local result [/file get dynu.checkip.html contents]\r\
    \n\r\
    \n# parse the current IP result\r\
    \n:local resultLen [:len \$result]\r\
    \n:local startLoc [:find \$result \": \" -1]\r\
    \n:set startLoc (\$startLoc + 2)\r\
    \n:local currentIP [:pick \$result \$startLoc \$resultLen]\r\
    \n:global ddnsuser chihome\r\
    \n:global ddnspass \"NkN!TWy5K@tFUUs2Ct\"\r\
    \n:global ddnshost \"chihome.freeddns.org\"\r\
    \n:global ipddns [:resolve \$ddnshost];\r\
    \n\r\
    \n#:global ipddns\r\
    \n\r\
    \n:if (\$ipddns != \$currentIP) do={\r\
    \n:log info (\"DynuDDNS: IP-Dynu = \$ipddns\")\r\
    \n:log info (\"DynuDDNS: IP-Fresh = \$currentIP\")\r\
    \n:log info \"DynuDDNS: Update IP needed, Sending UPDATE...!\"\r\
    \n:global str \"/nic/update\?hostname=\$ddnshost&myip=\$currentIP\"\r\
    \n:log info \"currentIP is \$currentIP\"\r\
    \n/tool fetch address=api.dynu.com src-path=\$str mode=http user=\$ddnsuse\
    r password=\$ddnspass dst-path=(\"/Dynu.\".\$ddnshost)\r\
    \n:delay 1\r\
    \n:global str [/file find name=\"Dynu.\$ddnshost\"];\r\
    \n/file remove \$str\r\
    \n:global ipddns \$currentIP\r\
    \n:log info \"DynuDDNS: IP updated to \$currentIP!\"\r\
    \n} else={\r\
    \n:log info \"DynuDDNS: No change needed\";\r\
    \n}"
/tool mac-server
set allowed-interface-list=LAN
/tool mac-server mac-winbox
set allowed-interface-list=LAN
